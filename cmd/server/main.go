package main

import (
	"context"
	"exert-app-service/cmd/server/router"
	"exert-app-service/internal/auth"
	"exert-app-service/internal/program"
	"exert-app-service/internal/session"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/helmet"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/fiber/v2/middleware/requestid"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/joho/godotenv"
)

type EnvValues struct {
	host               string
	port               string
	user               string
	pw                 string
	dbname             string
	refreshTokenExpiry time.Duration
}

func main() {
	envFile := ".env"
	log.Println("Loading environment variables from .env file")
	if err := godotenv.Load(envFile); err != nil {
		log.Printf("Warning: Error loading %s file: %v", envFile, err)
	}

	log.SetFlags(log.LstdFlags | log.Lshortfile)

	envValues := getEnvValues()

	if envValues.host == "" ||
		envValues.port == "" ||
		envValues.user == "" ||
		envValues.pw == "" ||
		envValues.dbname == "" {
		log.Fatalln("Failed to load all environment variables.")
	}

	ctx := context.Background()

	url := fmt.Sprintf(
		"postgres://%s:%s@%s:%s/%s",
		envValues.user,
		envValues.pw,
		envValues.host,
		envValues.port,
		envValues.dbname)

	log.Println("Creating connection pool")
	pool, err := pgxpool.New(ctx, url)
	if err != nil {
		log.Fatalf("Unable to create connection pool: %v", err)
	}
	defer pool.Close()

	// Set up configs
	jwtConfig := auth.NewJWTConfig()

	log.Println("Setting up services")

	// Program related services
	wpService := program.NewWorkoutProgramService(ctx, pool)
	prService := program.NewProgramRoutineService(ctx, pool)
	pgService := program.NewProgramGroupService(ctx, pool)
	programService := program.NewProgramService(ctx, pool, wpService, prService, pgService)

	// Session related services
	wsService := session.NewWorkoutSessionService(pool, ctx)
	sgService := session.NewSessionGroupService(ctx, pool)
	ssService := session.NewSessionSetService(pool, ctx)
	sessionService := session.NewSessionService(pool, ctx, wsService, sgService, ssService)

	// JWT related services
	jwtService := auth.NewJWTService(jwtConfig)
	authService := auth.NewAuthService(pool, jwtService, envValues.refreshTokenExpiry)

	// Set up handlers
	log.Println("Setting up handlers")
	programHandler := program.NewProgramHandler(programService)
	sessionHandler := session.NewSessionHandler(sessionService)
	authHandler := auth.NewAuthHandler(authService)

	// Set up middlewares
	log.Println("Setting up middlewares")
	jwtAuthMiddleware := auth.JWTAuthMiddleware(jwtService)

	app := fiber.New()

	app.Use(requestid.New())
	app.Use(logger.New())
	app.Use(recover.New())
	app.Use(cors.New())
	app.Use(helmet.New())

	log.Println("Mounting router for the endpoint /v1/api/")
	v1 := app.Group("/v1/api")
	router.V1Router(v1, programHandler, sessionHandler, authHandler, jwtAuthMiddleware(func(c *fiber.Ctx) error {
		return c.Next()
	}))

	log.Println("Starting server on port 8080")
	err = app.Listen(":8080")

	if err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

func getEnvValues() EnvValues {
	expiry, err := time.ParseDuration(os.Getenv("REFRESH_TOKEN_EXPIRY"))
	if err != nil {
		log.Printf("Warning: Error parsing REFRESH_TOKEN_EXPIRY: %v", err)
		expiry = 7 * 24 * time.Hour // Default to 7 days if parsing fails
	}

	return EnvValues{
		host:               os.Getenv("DB_HOST"),
		port:               os.Getenv("DB_PORT"),
		user:               os.Getenv("DB_USER"),
		pw:                 os.Getenv("DB_PASSWORD"),
		dbname:             os.Getenv("DB_NAME"),
		refreshTokenExpiry: expiry,
	}
}
