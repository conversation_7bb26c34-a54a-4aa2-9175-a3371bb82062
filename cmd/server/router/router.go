package router

import (
	"exert-app-service/internal/auth"
	"exert-app-service/internal/program"
	"exert-app-service/internal/session"

	"github.com/gofiber/fiber/v2"
)

func V1Router(r fiber.Router,
	programHandler *program.ProgramHandler,
	sessionHandler *session.SessionHandler,
	authHandler *auth.AuthHandler,
	jwtAuthMiddleware fiber.Handler,
) {
	// Public routes
	authGroup := r.Group("/auth")
	authGroup.Post("/register", authHandler.Register)
	authGroup.Post("/login", authHandler.Login)
	authGroup.Post("/refresh", authHandler.Refresh)
	authGroup.Post("/logout", authHandler.Logout)
	authGroup.Get("/me", authHandler.Me)

	// Protected routes
	sessionGroup := r.Group("/session")
	sessionGroup.Use(jwtAuthMiddleware)
	sessionGroup.Get("/", sessionHandler.ListAll)
	sessionGroup.Get("/:sessionId", sessionHandler.List)
	sessionGroup.Post("/", sessionHandler.Create)
	sessionGroup.Put("/", sessionHandler.Update)
	sessionGroup.Delete("/:sessionId", sessionHandler.Delete)

	programGroup := r.Group("/program")
	programGroup.Use(jwtAuthMiddleware)
	programGroup.Get("/", programHandler.ListAll)
	programGroup.Get("/:programId", programHandler.List)
	programGroup.Post("/", programHandler.Create)
	programGroup.Put("/", programHandler.Update)
	programGroup.Delete("/:programId", programHandler.Delete)
}
