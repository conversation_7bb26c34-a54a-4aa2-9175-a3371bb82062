package testutil

import (
	"context"
	"database/sql"
	"log"
	"net/http"
	"os"
	"testing"
	"time"

	"exert-app-service/cmd/server/router"
	"exert-app-service/internal/auth"
	"exert-app-service/internal/program"
	"exert-app-service/internal/session"

	"github.com/gofiber/fiber/v2"
	"github.com/jackc/pgx/v5/pgxpool"
	_ "github.com/jackc/pgx/v5/stdlib"
	"github.com/pressly/goose/v3"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
)

type TestSetup struct {
	App        *fiber.App
	Pool       *pgxpool.Pool
	AuthToken  auth.TokenResponse
	HttpClient *http.Client
}

type envValues struct {
	host               string
	port               string
	user               string
	pw                 string
	dbname             string
	refreshTokenExpiry string
}

func getEnvValues() envValues {
	return envValues{
		host:               os.Getenv("DB_HOST"),
		port:               os.Getenv("DB_PORT"),
		user:               os.Getenv("DB_USER"),
		pw:                 os.Getenv("DB_PASSWORD"),
		dbname:             os.Getenv("DB_NAME"),
		refreshTokenExpiry: os.Getenv("REFRESH_TOKEN_EXPIRY"),
	}
}

func SetupTestDB(t *testing.T) (testcontainers.Container, string) {
	ctx := context.Background()
	req := testcontainers.ContainerRequest{
		Image:        "postgres:16",
		ExposedPorts: []string{"5432/tcp"},
		Env: map[string]string{
			"POSTGRES_DB":       "testdb",
			"POSTGRES_USER":     "testuser",
			"POSTGRES_PASSWORD": "testpass",
		},
		WaitingFor: wait.ForAll(
			wait.ForLog("database system is ready to accept connections"),
			wait.ForListeningPort("5432/tcp"),
		),
	}

	postgresContainer, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	require.NoError(t, err)

	host, err := postgresContainer.Host(ctx)
	require.NoError(t, err)
	port, err := postgresContainer.MappedPort(ctx, "5432")
	require.NoError(t, err)

	t.Setenv("DB_HOST", host)
	t.Setenv("DB_PORT", port.Port())
	t.Setenv("DB_USER", "testuser")
	t.Setenv("DB_PASSWORD", "testpass")
	t.Setenv("DB_NAME", "testdb")
	t.Setenv("REFRESH_TOKEN_EXPIRY", "24h")

	envValues := getEnvValues()
	url := "postgres://" + envValues.user + ":" + envValues.pw + "@" + envValues.host + ":" + envValues.port + "/" + envValues.dbname + "?sslmode=disable"

	db, err := sql.Open("pgx", url)
	require.NoError(t, err)
	defer db.Close()

	goose.SetDialect("postgres")
	if err := goose.Up(db, "../../sqlc/migrations"); err != nil {
		log.Fatalf("goose up: %v", err)
	}

	return postgresContainer, url
}

func SetupTestRouter(pool *pgxpool.Pool) *fiber.App {
	ctx := context.Background()
	jwtConfig := auth.NewJWTConfig()

	// Program related services
	wpService := program.NewWorkoutProgramService(ctx, pool)
	prService := program.NewProgramRoutineService(ctx, pool)
	pgService := program.NewProgramGroupService(ctx, pool)
	programService := program.NewProgramService(ctx, pool, wpService, prService, pgService)

	// Session related services
	wsService := &session.WorkoutSessionService{Ctx: ctx, Pool: pool}
	sgService := &session.SessionGroupService{Ctx: ctx, Pool: pool}
	ssService := &session.SessionSetService{Ctx: ctx, Pool: pool}
	sessionService := &session.SessionService{
		Ctx:  ctx,
		Pool: pool,
		Wss:  wsService,
		Sgs:  sgService,
		Ss:   ssService,
	}
	jwtService := auth.NewJWTService(jwtConfig)
	envValues := getEnvValues()
	refreshTokenExpiry, _ := time.ParseDuration(envValues.refreshTokenExpiry)
	authService := auth.NewAuthService(pool, jwtService, refreshTokenExpiry)

	programHandler := &program.ProgramHandler{Ps: programService}
	sessionHandler := &session.SessionHandler{Ss: sessionService}
	authHandler := auth.NewAuthHandler(authService)

	app := fiber.New()
	jwtMiddleware := auth.JWTAuthMiddleware(jwtService)
	v1 := app.Group("/v1/api")
	router.V1Router(v1, programHandler, sessionHandler, authHandler, jwtMiddleware(func(c *fiber.Ctx) error {
		return c.Next()
	}))
	return app
}

func SetupTest(t *testing.T) *TestSetup {
	container, url := SetupTestDB(t)
	pool, err := pgxpool.New(context.Background(), url)
	require.NoError(t, err)

	router := SetupTestRouter(pool)

	// Register and login a test user
	RegisterUser(t, router, "testuser", "<EMAIL>", "testpassword")
	token := LoginUser(t, router, "testuser", "testpassword")

	// Ensure container and connection are terminated when test is done
	t.Cleanup(func() {
		pool.Close()
		container.Terminate(context.Background())
	})

	return &TestSetup{
		App:        router,
		Pool:       pool,
		AuthToken:  token,
		HttpClient: &http.Client{},
	}
}
