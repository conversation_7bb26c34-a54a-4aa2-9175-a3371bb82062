// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0

package db

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type ProgramGroup struct {
	ID         int32
	CreatedAt  pgtype.Timestamp
	UpdatedAt  pgtype.Timestamp
	DeletedAt  pgtype.Timestamp
	Name       string
	Sets       int32
	Reps       int32
	GroupOrder int32
	RoutineID  int32
	UserID     pgtype.Int4
}

type ProgramRoutine struct {
	ID           int32
	CreatedAt    pgtype.Timestamp
	UpdatedAt    pgtype.Timestamp
	DeletedAt    pgtype.Timestamp
	Name         string
	Description  pgtype.Text
	RoutineOrder int32
	ProgramID    int32
	UserID       pgtype.Int4
}

type RefreshToken struct {
	ID        int32
	UserID    int32
	Token     string
	ExpiresAt pgtype.Timestamptz
	CreatedAt pgtype.Timestamptz
	Revoked   bool
}

type SessionGroup struct {
	ID          int32
	CreatedAt   pgtype.Timestamp
	UpdatedAt   pgtype.Timestamp
	DeletedAt   pgtype.Timestamp
	Name        string
	Description pgtype.Text
	GroupOrder  int32
	SessionID   int32
	UserID      pgtype.Int4
}

type SessionSet struct {
	ID        int32
	CreatedAt pgtype.Timestamp
	UpdatedAt pgtype.Timestamp
	DeletedAt pgtype.Timestamp
	Reps      int32
	Weight    int32
	SetOrder  int32
	GroupID   int32
	UserID    pgtype.Int4
}

type User struct {
	ID           int32
	Username     string
	Email        string
	PasswordHash string
	CreatedAt    pgtype.Timestamptz
	UpdatedAt    pgtype.Timestamptz
}

type WorkoutProgram struct {
	ID          int32
	CreatedAt   pgtype.Timestamp
	UpdatedAt   pgtype.Timestamp
	DeletedAt   pgtype.Timestamp
	Name        string
	Description pgtype.Text
	UserID      pgtype.Int4
}

type WorkoutSession struct {
	ID          int32
	CreatedAt   pgtype.Timestamp
	UpdatedAt   pgtype.Timestamp
	DeletedAt   pgtype.Timestamp
	Name        string
	Description pgtype.Text
	StartTime   pgtype.Timestamp
	EndTime     pgtype.Timestamp
	UserID      pgtype.Int4
	ProgramID   pgtype.Int4
}
