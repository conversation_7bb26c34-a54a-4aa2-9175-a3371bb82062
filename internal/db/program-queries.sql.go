// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: program-queries.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createProgram = `-- name: CreateProgram :one
INSERT INTO workout_programs (name,
                              description,
                              user_id)
VALUES ($1,
        $2,
        $3)
RETURNING id, created_at, updated_at, deleted_at, name, description, user_id
`

type CreateProgramParams struct {
	Name        string
	Description pgtype.Text
	UserID      pgtype.Int4
}

func (q *Queries) CreateProgram(ctx context.Context, arg CreateProgramParams) (WorkoutProgram, error) {
	row := q.db.QueryRow(ctx, createProgram, arg.Name, arg.Description, arg.UserID)
	var i WorkoutProgram
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Name,
		&i.Description,
		&i.User<PERSON>,
	)
	return i, err
}

const createProgramGroup = `-- name: CreateProgramGroup :one
INSERT INTO program_groups (name,
                            sets,
                            reps,
                            group_order,
                            routine_id,
                            user_id)
VALUES ($1,
        $2,
        $3,
        $4,
        $5,
        $6)
RETURNING id, created_at, updated_at, deleted_at, name, sets, reps, group_order, routine_id, user_id
`

type CreateProgramGroupParams struct {
	Name       string
	Sets       int32
	Reps       int32
	GroupOrder int32
	RoutineID  int32
	UserID     pgtype.Int4
}

func (q *Queries) CreateProgramGroup(ctx context.Context, arg CreateProgramGroupParams) (ProgramGroup, error) {
	row := q.db.QueryRow(ctx, createProgramGroup,
		arg.Name,
		arg.Sets,
		arg.Reps,
		arg.GroupOrder,
		arg.RoutineID,
		arg.UserID,
	)
	var i ProgramGroup
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Name,
		&i.Sets,
		&i.Reps,
		&i.GroupOrder,
		&i.RoutineID,
		&i.UserID,
	)
	return i, err
}

const createProgramRoutine = `-- name: CreateProgramRoutine :one
INSERT INTO program_routines (name,
                              description,
                              routine_order,
                              program_id,
                              user_id)
VALUES ($1,
        $2,
        $3,
        $4,
        $5)
RETURNING id, created_at, updated_at, deleted_at, name, description, routine_order, program_id, user_id
`

type CreateProgramRoutineParams struct {
	Name         string
	Description  pgtype.Text
	RoutineOrder int32
	ProgramID    int32
	UserID       pgtype.Int4
}

func (q *Queries) CreateProgramRoutine(ctx context.Context, arg CreateProgramRoutineParams) (ProgramRoutine, error) {
	row := q.db.QueryRow(ctx, createProgramRoutine,
		arg.Name,
		arg.Description,
		arg.RoutineOrder,
		arg.ProgramID,
		arg.UserID,
	)
	var i ProgramRoutine
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Name,
		&i.Description,
		&i.RoutineOrder,
		&i.ProgramID,
		&i.UserID,
	)
	return i, err
}

const deleteProgram = `-- name: DeleteProgram :exec
DELETE
FROM workout_programs
WHERE id = $1 AND user_id = $2
`

type DeleteProgramParams struct {
	ID     int32
	UserID pgtype.Int4
}

func (q *Queries) DeleteProgram(ctx context.Context, arg DeleteProgramParams) error {
	_, err := q.db.Exec(ctx, deleteProgram, arg.ID, arg.UserID)
	return err
}

const deleteProgramGroupsNotInSlice = `-- name: DeleteProgramGroupsNotInSlice :exec
DELETE
FROM program_groups AS pg
WHERE routine_id = $1
  AND user_id = $2
  AND NOT EXISTS (SELECT 1
                  FROM UNNEST($3::int[]) AS id
                  WHERE id = pg.id)
`

type DeleteProgramGroupsNotInSliceParams struct {
	RoutineID int32
	UserID    pgtype.Int4
	Column3   []int32
}

func (q *Queries) DeleteProgramGroupsNotInSlice(ctx context.Context, arg DeleteProgramGroupsNotInSliceParams) error {
	_, err := q.db.Exec(ctx, deleteProgramGroupsNotInSlice, arg.RoutineID, arg.UserID, arg.Column3)
	return err
}

const deleteProgramRoutinesNotInSlice = `-- name: DeleteProgramRoutinesNotInSlice :exec
DELETE
FROM program_routines AS pr
WHERE program_id = $1
  AND user_id = $2
  AND NOT EXISTS (SELECT 1
                  FROM UNNEST($3::int[]) AS id
                  WHERE id = pr.id)
`

type DeleteProgramRoutinesNotInSliceParams struct {
	ProgramID int32
	UserID    pgtype.Int4
	Column3   []int32
}

func (q *Queries) DeleteProgramRoutinesNotInSlice(ctx context.Context, arg DeleteProgramRoutinesNotInSliceParams) error {
	_, err := q.db.Exec(ctx, deleteProgramRoutinesNotInSlice, arg.ProgramID, arg.UserID, arg.Column3)
	return err
}

const getProgramGroup = `-- name: GetProgramGroup :many
select id, created_at, updated_at, deleted_at, name, sets, reps, group_order, routine_id, user_id
from program_groups as pg
where pg.id = $1 AND pg.user_id = $2
`

type GetProgramGroupParams struct {
	ID     int32
	UserID pgtype.Int4
}

func (q *Queries) GetProgramGroup(ctx context.Context, arg GetProgramGroupParams) ([]ProgramGroup, error) {
	rows, err := q.db.Query(ctx, getProgramGroup, arg.ID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ProgramGroup
	for rows.Next() {
		var i ProgramGroup
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Name,
			&i.Sets,
			&i.Reps,
			&i.GroupOrder,
			&i.RoutineID,
			&i.UserID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getProgramRoutine = `-- name: GetProgramRoutine :many
select id, created_at, updated_at, deleted_at, name, description, routine_order, program_id, user_id
from program_routines as pr
where pr.id = $1 AND pr.user_id = $2
`

type GetProgramRoutineParams struct {
	ID     int32
	UserID pgtype.Int4
}

func (q *Queries) GetProgramRoutine(ctx context.Context, arg GetProgramRoutineParams) ([]ProgramRoutine, error) {
	rows, err := q.db.Query(ctx, getProgramRoutine, arg.ID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ProgramRoutine
	for rows.Next() {
		var i ProgramRoutine
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Name,
			&i.Description,
			&i.RoutineOrder,
			&i.ProgramID,
			&i.UserID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getWorkoutProgram = `-- name: GetWorkoutProgram :many
select wp.id,
       wp.name,
       wp.description,
       pr.id          as routine_id,
       pr.name        as routine_name,
       pr.description as routine_description,
       pr.routine_order,
       pr.program_id  as routine_program_id,
       pg.id          as group_id,
       pg.name        as group_name,
       pg.sets,
       pg.reps,
       pg.group_order,
       pg.routine_id  as group_routine_id
from workout_programs as wp
         inner join program_routines as pr on
    wp.id = pr.program_id
         inner join program_groups as pg on
    pr.id = pg.routine_id
where wp.id = $1 AND wp.user_id = $2
`

type GetWorkoutProgramParams struct {
	ID     int32
	UserID pgtype.Int4
}

type GetWorkoutProgramRow struct {
	ID                 int32
	Name               string
	Description        pgtype.Text
	RoutineID          int32
	RoutineName        string
	RoutineDescription pgtype.Text
	RoutineOrder       int32
	RoutineProgramID   int32
	GroupID            int32
	GroupName          string
	Sets               int32
	Reps               int32
	GroupOrder         int32
	GroupRoutineID     int32
}

func (q *Queries) GetWorkoutProgram(ctx context.Context, arg GetWorkoutProgramParams) ([]GetWorkoutProgramRow, error) {
	rows, err := q.db.Query(ctx, getWorkoutProgram, arg.ID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetWorkoutProgramRow
	for rows.Next() {
		var i GetWorkoutProgramRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.RoutineID,
			&i.RoutineName,
			&i.RoutineDescription,
			&i.RoutineOrder,
			&i.RoutineProgramID,
			&i.GroupID,
			&i.GroupName,
			&i.Sets,
			&i.Reps,
			&i.GroupOrder,
			&i.GroupRoutineID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getWorkoutPrograms = `-- name: GetWorkoutPrograms :many
select wp.id,
       wp.name,
       wp.description,
       pr.id          as routine_id,
       pr.name        as routine_name,
       pr.description as routine_description,
       pr.routine_order,
       pr.program_id  as routine_program_id,
       pg.id          as group_id,
       pg.name        as group_name,
       pg.sets,
       pg.reps,
       pg.group_order,
       pg.routine_id  as group_routine_id
from workout_programs as wp
         inner join program_routines as pr on
    wp.id = pr.program_id
         inner join program_groups as pg on
    pr.id = pg.routine_id
where wp.user_id = $1
`

type GetWorkoutProgramsRow struct {
	ID                 int32
	Name               string
	Description        pgtype.Text
	RoutineID          int32
	RoutineName        string
	RoutineDescription pgtype.Text
	RoutineOrder       int32
	RoutineProgramID   int32
	GroupID            int32
	GroupName          string
	Sets               int32
	Reps               int32
	GroupOrder         int32
	GroupRoutineID     int32
}

func (q *Queries) GetWorkoutPrograms(ctx context.Context, userID pgtype.Int4) ([]GetWorkoutProgramsRow, error) {
	rows, err := q.db.Query(ctx, getWorkoutPrograms, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetWorkoutProgramsRow
	for rows.Next() {
		var i GetWorkoutProgramsRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.RoutineID,
			&i.RoutineName,
			&i.RoutineDescription,
			&i.RoutineOrder,
			&i.RoutineProgramID,
			&i.GroupID,
			&i.GroupName,
			&i.Sets,
			&i.Reps,
			&i.GroupOrder,
			&i.GroupRoutineID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateProgram = `-- name: UpdateProgram :one
UPDATE workout_programs
SET name         = $1,
    description  = $2
WHERE id = $3 AND user_id = $4
RETURNING id, created_at, updated_at, deleted_at, name, description, user_id
`

type UpdateProgramParams struct {
	Name        string
	Description pgtype.Text
	ID          int32
	UserID      pgtype.Int4
}

func (q *Queries) UpdateProgram(ctx context.Context, arg UpdateProgramParams) (WorkoutProgram, error) {
	row := q.db.QueryRow(ctx, updateProgram,
		arg.Name,
		arg.Description,
		arg.ID,
		arg.UserID,
	)
	var i WorkoutProgram
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Name,
		&i.Description,
		&i.UserID,
	)
	return i, err
}

const updateProgramGroup = `-- name: UpdateProgramGroup :one
UPDATE program_groups
SET name        = $1,
    sets        = $2,
    reps        = $3,
    group_order = $4
WHERE id = $5 AND user_id = $6
RETURNING id, created_at, updated_at, deleted_at, name, sets, reps, group_order, routine_id, user_id
`

type UpdateProgramGroupParams struct {
	Name       string
	Sets       int32
	Reps       int32
	GroupOrder int32
	ID         int32
	UserID     pgtype.Int4
}

func (q *Queries) UpdateProgramGroup(ctx context.Context, arg UpdateProgramGroupParams) (ProgramGroup, error) {
	row := q.db.QueryRow(ctx, updateProgramGroup,
		arg.Name,
		arg.Sets,
		arg.Reps,
		arg.GroupOrder,
		arg.ID,
		arg.UserID,
	)
	var i ProgramGroup
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Name,
		&i.Sets,
		&i.Reps,
		&i.GroupOrder,
		&i.RoutineID,
		&i.UserID,
	)
	return i, err
}

const updateProgramRoutine = `-- name: UpdateProgramRoutine :one
UPDATE program_routines
SET name          = $1,
    description   = $2,
    routine_order = $3
WHERE id = $4 AND user_id = $5
RETURNING id, created_at, updated_at, deleted_at, name, description, routine_order, program_id, user_id
`

type UpdateProgramRoutineParams struct {
	Name         string
	Description  pgtype.Text
	RoutineOrder int32
	ID           int32
	UserID       pgtype.Int4
}

func (q *Queries) UpdateProgramRoutine(ctx context.Context, arg UpdateProgramRoutineParams) (ProgramRoutine, error) {
	row := q.db.QueryRow(ctx, updateProgramRoutine,
		arg.Name,
		arg.Description,
		arg.RoutineOrder,
		arg.ID,
		arg.UserID,
	)
	var i ProgramRoutine
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Name,
		&i.Description,
		&i.RoutineOrder,
		&i.ProgramID,
		&i.UserID,
	)
	return i, err
}
