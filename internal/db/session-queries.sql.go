// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: session-queries.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createSessionGroup = `-- name: CreateSessionGroup :one
INSERT INTO session_groups
    (name, description, group_order, session_id, user_id)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, created_at, updated_at, deleted_at, name, description, group_order, session_id, user_id
`

type CreateSessionGroupParams struct {
	Name        string
	Description pgtype.Text
	GroupOrder  int32
	SessionID   int32
	UserID      pgtype.Int4
}

func (q *Queries) CreateSessionGroup(ctx context.Context, arg CreateSessionGroupParams) (SessionGroup, error) {
	row := q.db.QueryRow(ctx, createSessionGroup,
		arg.Name,
		arg.Description,
		arg.GroupOrder,
		arg.SessionID,
		arg.UserID,
	)
	var i SessionGroup
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Name,
		&i.Description,
		&i.GroupOrder,
		&i.SessionID,
		&i.UserID,
	)
	return i, err
}

const createSessionSet = `-- name: CreateSessionSet :one
INSERT INTO session_sets
    (reps, weight, set_order, group_id, user_id)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, created_at, updated_at, deleted_at, reps, weight, set_order, group_id, user_id
`

type CreateSessionSetParams struct {
	Reps     int32
	Weight   int32
	SetOrder int32
	GroupID  int32
	UserID   pgtype.Int4
}

func (q *Queries) CreateSessionSet(ctx context.Context, arg CreateSessionSetParams) (SessionSet, error) {
	row := q.db.QueryRow(ctx, createSessionSet,
		arg.Reps,
		arg.Weight,
		arg.SetOrder,
		arg.GroupID,
		arg.UserID,
	)
	var i SessionSet
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Reps,
		&i.Weight,
		&i.SetOrder,
		&i.GroupID,
		&i.UserID,
	)
	return i, err
}

const createWorkoutSession = `-- name: CreateWorkoutSession :one
INSERT INTO workout_sessions
    (name, description, start_time, end_time, program_id, user_id)
VALUES ($1, $2, $3, $4, $5, $6)
RETURNING id, created_at, updated_at, deleted_at, name, description, start_time, end_time, user_id, program_id
`

type CreateWorkoutSessionParams struct {
	Name        string
	Description pgtype.Text
	StartTime   pgtype.Timestamp
	EndTime     pgtype.Timestamp
	ProgramID   pgtype.Int4
	UserID      pgtype.Int4
}

func (q *Queries) CreateWorkoutSession(ctx context.Context, arg CreateWorkoutSessionParams) (WorkoutSession, error) {
	row := q.db.QueryRow(ctx, createWorkoutSession,
		arg.Name,
		arg.Description,
		arg.StartTime,
		arg.EndTime,
		arg.ProgramID,
		arg.UserID,
	)
	var i WorkoutSession
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Name,
		&i.Description,
		&i.StartTime,
		&i.EndTime,
		&i.UserID,
		&i.ProgramID,
	)
	return i, err
}

const deleteSession = `-- name: DeleteSession :exec
DELETE
FROM workout_sessions
WHERE id = $1 AND user_id = $2
`

type DeleteSessionParams struct {
	ID     int32
	UserID pgtype.Int4
}

func (q *Queries) DeleteSession(ctx context.Context, arg DeleteSessionParams) error {
	_, err := q.db.Exec(ctx, deleteSession, arg.ID, arg.UserID)
	return err
}

const deleteSessionGroupsNotInSlice = `-- name: DeleteSessionGroupsNotInSlice :exec
DELETE
FROM session_groups AS sg
WHERE session_id = $1
  AND sg.user_id = $2
  AND NOT EXISTS (SELECT 1
                  FROM UNNEST($3::int[]) AS id
                  WHERE id = sg.id)
`

type DeleteSessionGroupsNotInSliceParams struct {
	SessionID int32
	UserID    pgtype.Int4
	Column3   []int32
}

func (q *Queries) DeleteSessionGroupsNotInSlice(ctx context.Context, arg DeleteSessionGroupsNotInSliceParams) error {
	_, err := q.db.Exec(ctx, deleteSessionGroupsNotInSlice, arg.SessionID, arg.UserID, arg.Column3)
	return err
}

const deleteSessionSetsNotInSlice = `-- name: DeleteSessionSetsNotInSlice :exec
DELETE
FROM session_sets AS ss
WHERE group_id = $1
  AND ss.user_id = $2
  AND NOT EXISTS (SELECT 1
                  FROM UNNEST($3::int[]) AS id
                  WHERE id = ss.id)
`

type DeleteSessionSetsNotInSliceParams struct {
	GroupID int32
	UserID  pgtype.Int4
	Column3 []int32
}

func (q *Queries) DeleteSessionSetsNotInSlice(ctx context.Context, arg DeleteSessionSetsNotInSliceParams) error {
	_, err := q.db.Exec(ctx, deleteSessionSetsNotInSlice, arg.GroupID, arg.UserID, arg.Column3)
	return err
}

const getSessionGroup = `-- name: GetSessionGroup :many
select id, created_at, updated_at, deleted_at, name, description, group_order, session_id, user_id
from session_groups as sg
where sg.id = $1 AND sg.user_id = $2
`

type GetSessionGroupParams struct {
	ID     int32
	UserID pgtype.Int4
}

func (q *Queries) GetSessionGroup(ctx context.Context, arg GetSessionGroupParams) ([]SessionGroup, error) {
	rows, err := q.db.Query(ctx, getSessionGroup, arg.ID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SessionGroup
	for rows.Next() {
		var i SessionGroup
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Name,
			&i.Description,
			&i.GroupOrder,
			&i.SessionID,
			&i.UserID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSessionSet = `-- name: GetSessionSet :many
select id, created_at, updated_at, deleted_at, reps, weight, set_order, group_id, user_id
from session_sets as ss
where ss.id = $1 AND ss.user_id = $2
`

type GetSessionSetParams struct {
	ID     int32
	UserID pgtype.Int4
}

func (q *Queries) GetSessionSet(ctx context.Context, arg GetSessionSetParams) ([]SessionSet, error) {
	rows, err := q.db.Query(ctx, getSessionSet, arg.ID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SessionSet
	for rows.Next() {
		var i SessionSet
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Reps,
			&i.Weight,
			&i.SetOrder,
			&i.GroupID,
			&i.UserID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getWorkoutSession = `-- name: GetWorkoutSession :many
select ws.id,
       ws.name,
       ws.description,
       ws.start_time,
       ws.end_time,
       ws.program_id,
       sg.id          as group_id,
       sg.name        as group_name,
       sg.description as group_description,
       sg.group_order,
       sg.session_id  as group_session_id,
       ss.id          as set_id,
       ss.reps,
       ss.weight,
       ss.set_order,
       ss.group_id    as set_group_id
from workout_sessions as ws
         inner join public.session_groups sg on
    ws.id = sg.session_id
         inner join public.session_sets ss on
    sg.id = ss.group_id
where ws.id = $1 AND ws.user_id = $2
`

type GetWorkoutSessionParams struct {
	ID     int32
	UserID pgtype.Int4
}

type GetWorkoutSessionRow struct {
	ID               int32
	Name             string
	Description      pgtype.Text
	StartTime        pgtype.Timestamp
	EndTime          pgtype.Timestamp
	ProgramID        pgtype.Int4
	GroupID          int32
	GroupName        string
	GroupDescription pgtype.Text
	GroupOrder       int32
	GroupSessionID   int32
	SetID            int32
	Reps             int32
	Weight           int32
	SetOrder         int32
	SetGroupID       int32
}

func (q *Queries) GetWorkoutSession(ctx context.Context, arg GetWorkoutSessionParams) ([]GetWorkoutSessionRow, error) {
	rows, err := q.db.Query(ctx, getWorkoutSession, arg.ID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetWorkoutSessionRow
	for rows.Next() {
		var i GetWorkoutSessionRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.StartTime,
			&i.EndTime,
			&i.ProgramID,
			&i.GroupID,
			&i.GroupName,
			&i.GroupDescription,
			&i.GroupOrder,
			&i.GroupSessionID,
			&i.SetID,
			&i.Reps,
			&i.Weight,
			&i.SetOrder,
			&i.SetGroupID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getWorkoutSessions = `-- name: GetWorkoutSessions :many
select ws.id,
       ws.name,
       ws.description,
       ws.start_time,
       ws.end_time,
       ws.program_id,
       sg.id          as group_id,
       sg.name        as group_name,
       sg.description as group_description,
       sg.group_order,
       sg.session_id  as group_session_id,
       ss.id          as set_id,
       ss.reps,
       ss.weight,
       ss.set_order,
       ss.group_id    as set_group_id
from workout_sessions as ws
         inner join public.session_groups sg on
    ws.id = sg.session_id
         inner join public.session_sets ss on
    sg.id = ss.group_id
where ws.user_id = $1
`

type GetWorkoutSessionsRow struct {
	ID               int32
	Name             string
	Description      pgtype.Text
	StartTime        pgtype.Timestamp
	EndTime          pgtype.Timestamp
	ProgramID        pgtype.Int4
	GroupID          int32
	GroupName        string
	GroupDescription pgtype.Text
	GroupOrder       int32
	GroupSessionID   int32
	SetID            int32
	Reps             int32
	Weight           int32
	SetOrder         int32
	SetGroupID       int32
}

func (q *Queries) GetWorkoutSessions(ctx context.Context, userID pgtype.Int4) ([]GetWorkoutSessionsRow, error) {
	rows, err := q.db.Query(ctx, getWorkoutSessions, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetWorkoutSessionsRow
	for rows.Next() {
		var i GetWorkoutSessionsRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.StartTime,
			&i.EndTime,
			&i.ProgramID,
			&i.GroupID,
			&i.GroupName,
			&i.GroupDescription,
			&i.GroupOrder,
			&i.GroupSessionID,
			&i.SetID,
			&i.Reps,
			&i.Weight,
			&i.SetOrder,
			&i.SetGroupID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateSessionGroup = `-- name: UpdateSessionGroup :one
UPDATE session_groups
SET name        = $1,
    description = $2,
    group_order = $3
WHERE id = $4 AND user_id = $5
RETURNING id, created_at, updated_at, deleted_at, name, description, group_order, session_id, user_id
`

type UpdateSessionGroupParams struct {
	Name        string
	Description pgtype.Text
	GroupOrder  int32
	ID          int32
	UserID      pgtype.Int4
}

func (q *Queries) UpdateSessionGroup(ctx context.Context, arg UpdateSessionGroupParams) (SessionGroup, error) {
	row := q.db.QueryRow(ctx, updateSessionGroup,
		arg.Name,
		arg.Description,
		arg.GroupOrder,
		arg.ID,
		arg.UserID,
	)
	var i SessionGroup
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Name,
		&i.Description,
		&i.GroupOrder,
		&i.SessionID,
		&i.UserID,
	)
	return i, err
}

const updateSessionSet = `-- name: UpdateSessionSet :one
UPDATE session_sets
SET reps      = $1,
    weight    = $2,
    set_order = $3
WHERE id = $4 AND user_id = $5
RETURNING id, created_at, updated_at, deleted_at, reps, weight, set_order, group_id, user_id
`

type UpdateSessionSetParams struct {
	Reps     int32
	Weight   int32
	SetOrder int32
	ID       int32
	UserID   pgtype.Int4
}

func (q *Queries) UpdateSessionSet(ctx context.Context, arg UpdateSessionSetParams) (SessionSet, error) {
	row := q.db.QueryRow(ctx, updateSessionSet,
		arg.Reps,
		arg.Weight,
		arg.SetOrder,
		arg.ID,
		arg.UserID,
	)
	var i SessionSet
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Reps,
		&i.Weight,
		&i.SetOrder,
		&i.GroupID,
		&i.UserID,
	)
	return i, err
}

const updateWorkoutSession = `-- name: UpdateWorkoutSession :one
UPDATE workout_sessions
SET name        = $1,
    description = $2,
    start_time  = $3,
    end_time    = $4,
    program_id  = $5
WHERE id = $6 AND user_id = $7
RETURNING id, created_at, updated_at, deleted_at, name, description, start_time, end_time, user_id, program_id
`

type UpdateWorkoutSessionParams struct {
	Name        string
	Description pgtype.Text
	StartTime   pgtype.Timestamp
	EndTime     pgtype.Timestamp
	ProgramID   pgtype.Int4
	ID          int32
	UserID      pgtype.Int4
}

func (q *Queries) UpdateWorkoutSession(ctx context.Context, arg UpdateWorkoutSessionParams) (WorkoutSession, error) {
	row := q.db.QueryRow(ctx, updateWorkoutSession,
		arg.Name,
		arg.Description,
		arg.StartTime,
		arg.EndTime,
		arg.ProgramID,
		arg.ID,
		arg.UserID,
	)
	var i WorkoutSession
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Name,
		&i.Description,
		&i.StartTime,
		&i.EndTime,
		&i.UserID,
		&i.ProgramID,
	)
	return i, err
}
