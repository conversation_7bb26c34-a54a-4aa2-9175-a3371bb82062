-- +goose Up
CREATE TABLE IF NOT EXISTS workout_programs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    name VA<PERSON>HAR NOT NULL,
    description TEXT
);

CREATE TABLE IF NOT EXISTS workout_sessions (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    name VARCHAR NOT NULL,
    description TEXT,
    start_time TIMESTAMP,
    end_time TIMESTAMP
);

CREATE TABLE IF NOT EXISTS program_routines (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    name <PERSON><PERSON><PERSON><PERSON> NOT NULL,
    description TEXT,
    routine_order INT NOT NULL,
    program_id INT NOT NULL REFERENCES workout_programs(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS program_groups (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP DEFAULT NULL,
    name VARCHAR NOT NULL,
    sets INT NOT NULL,
    reps INT NOT NULL,
    group_order INT NOT NULL,
    routine_id INT NOT NULL REFERENCES program_routines(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS session_groups (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP DEFAULT NULL,
    name VARCHAR NOT NULL,
    description TEXT,
    group_order INT NOT NULL,
    session_id INT NOT NULL REFERENCES workout_sessions(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS session_sets (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    reps INT NOT NULL,
    weight INT NOT NULL,
    set_order INT NOT NULL,
    group_id INT NOT NULL REFERENCES session_groups(id) ON DELETE CASCADE
);

-- +goose Down
DROP TABLE IF EXISTS session_sets;
DROP TABLE IF EXISTS session_groups;
DROP TABLE IF EXISTS program_groups;
DROP TABLE IF EXISTS program_routines;
DROP TABLE IF EXISTS workout_sessions;
DROP TABLE IF EXISTS workout_programs;