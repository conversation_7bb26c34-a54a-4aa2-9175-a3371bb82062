-- +goose Up

-- Add user_id to workout_programs table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'workout_programs' AND column_name = 'user_id'
    ) THEN
        ALTER TABLE workout_programs
        ADD COLUMN user_id INTEGER REFERENCES users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add user_id to program_routines table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'program_routines' AND column_name = 'user_id'
    ) THEN
        ALTER TABLE program_routines
        ADD COLUMN user_id INTEGER REFERENCES users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add user_id to program_groups table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'program_groups' AND column_name = 'user_id'
    ) THEN
        ALTER TABLE program_groups
        ADD COLUMN user_id INTEGER REFERENCES users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add user_id to workout_sessions table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'workout_sessions' AND column_name = 'user_id'
    ) THEN
        ALTER TABLE workout_sessions
        ADD COLUMN user_id INTEGER REFERENCES users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add user_id to session_groups table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'session_groups' AND column_name = 'user_id'
    ) THEN
        ALTER TABLE session_groups
        ADD COLUMN user_id INTEGER REFERENCES users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add user_id to session_sets table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'session_sets' AND column_name = 'user_id'
    ) THEN
        ALTER TABLE session_sets
        ADD COLUMN user_id INTEGER REFERENCES users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Create indexes for user_id columns if they don't exist
CREATE INDEX IF NOT EXISTS idx_workout_programs_user_id ON workout_programs(user_id);
CREATE INDEX IF NOT EXISTS idx_program_routines_user_id ON program_routines(user_id);
CREATE INDEX IF NOT EXISTS idx_program_groups_user_id ON program_groups(user_id);
CREATE INDEX IF NOT EXISTS idx_workout_sessions_user_id ON workout_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_session_groups_user_id ON session_groups(user_id);
CREATE INDEX IF NOT EXISTS idx_session_sets_user_id ON session_sets(user_id);

-- +goose Down
DROP INDEX IF EXISTS idx_workout_programs_user_id;
DROP INDEX IF EXISTS idx_program_routines_user_id;
DROP INDEX IF EXISTS idx_program_groups_user_id;
DROP INDEX IF EXISTS idx_workout_sessions_user_id;
DROP INDEX IF EXISTS idx_session_groups_user_id;
DROP INDEX IF EXISTS idx_session_sets_user_id;

ALTER TABLE workout_programs DROP COLUMN IF EXISTS user_id;
ALTER TABLE program_routines DROP COLUMN IF EXISTS user_id;
ALTER TABLE program_groups DROP COLUMN IF EXISTS user_id;
ALTER TABLE workout_sessions DROP COLUMN IF EXISTS user_id;
ALTER TABLE session_groups DROP COLUMN IF EXISTS user_id;
ALTER TABLE session_sets DROP COLUMN IF EXISTS user_id;
