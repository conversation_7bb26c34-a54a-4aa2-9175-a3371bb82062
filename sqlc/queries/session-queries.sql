-- name: GetWorkoutSessions :many
select ws.id,
       ws.name,
       ws.description,
       ws.start_time,
       ws.end_time,
       ws.program_id,
       sg.id          as group_id,
       sg.name        as group_name,
       sg.description as group_description,
       sg.group_order,
       sg.session_id  as group_session_id,
       ss.id          as set_id,
       ss.reps,
       ss.weight,
       ss.set_order,
       ss.group_id    as set_group_id
from workout_sessions as ws
         inner join public.session_groups sg on
    ws.id = sg.session_id
         inner join public.session_sets ss on
    sg.id = ss.group_id
where ws.user_id = $1;

-- name: GetWorkoutSession :many
select ws.id,
       ws.name,
       ws.description,
       ws.start_time,
       ws.end_time,
       ws.program_id,
       sg.id          as group_id,
       sg.name        as group_name,
       sg.description as group_description,
       sg.group_order,
       sg.session_id  as group_session_id,
       ss.id          as set_id,
       ss.reps,
       ss.weight,
       ss.set_order,
       ss.group_id    as set_group_id
from workout_sessions as ws
         inner join public.session_groups sg on
    ws.id = sg.session_id
         inner join public.session_sets ss on
    sg.id = ss.group_id
where ws.id = $1 AND ws.user_id = $2;

-- name: GetSessionGroup :many
select *
from session_groups as sg
where sg.id = $1 AND sg.user_id = $2;

-- name: GetSessionSet :many
select *
from session_sets as ss
where ss.id = $1 AND ss.user_id = $2;

-- name: CreateWorkoutSession :one
INSERT INTO workout_sessions
    (name, description, start_time, end_time, program_id, user_id)
VALUES ($1, $2, $3, $4, $5, $6)
RETURNING *;

-- name: CreateSessionGroup :one
INSERT INTO session_groups
    (name, description, group_order, session_id, user_id)
VALUES ($1, $2, $3, $4, $5)
RETURNING *;

-- name: CreateSessionSet :one
INSERT INTO session_sets
    (reps, weight, set_order, group_id, user_id)
VALUES ($1, $2, $3, $4, $5)
RETURNING *;

-- name: UpdateWorkoutSession :one
UPDATE workout_sessions
SET name        = $1,
    description = $2,
    start_time  = $3,
    end_time    = $4,
    program_id  = $5
WHERE id = $6 AND user_id = $7
RETURNING *;

-- name: UpdateSessionGroup :one
UPDATE session_groups
SET name        = $1,
    description = $2,
    group_order = $3
WHERE id = $4 AND user_id = $5
RETURNING *;

-- name: UpdateSessionSet :one
UPDATE session_sets
SET reps      = $1,
    weight    = $2,
    set_order = $3
WHERE id = $4 AND user_id = $5
RETURNING *;

-- name: DeleteSession :exec
DELETE
FROM workout_sessions
WHERE id = $1 AND user_id = $2;

-- name: DeleteSessionGroupsNotInSlice :exec
DELETE
FROM session_groups AS sg
WHERE session_id = $1
  AND sg.user_id = $2
  AND NOT EXISTS (SELECT 1
                  FROM UNNEST($3::int[]) AS id
                  WHERE id = sg.id);

-- name: DeleteSessionSetsNotInSlice :exec
DELETE
FROM session_sets AS ss
WHERE group_id = $1
  AND ss.user_id = $2
  AND NOT EXISTS (SELECT 1
                  FROM UNNEST($3::int[]) AS id
                  WHERE id = ss.id);
